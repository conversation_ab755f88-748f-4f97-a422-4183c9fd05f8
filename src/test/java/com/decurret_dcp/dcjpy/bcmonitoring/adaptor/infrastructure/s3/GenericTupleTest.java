package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.protocol.core.methods.response.AbiDefinition;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify the generic tuple handling functionality
 */
public class GenericTupleTest {

    private AbiParser abiParser;
    private ObjectMapper mapper;

    @BeforeEach
    void setUp() {
        BcmonitoringConfigurationProperties properties = new BcmonitoringConfigurationProperties();
        abiParser = new AbiParser(properties);
        mapper = new ObjectMapper();
    }

    @Test
    void testGenericTupleTypeReferenceCreation() throws IOException {
        // Test ABI JSON with a simple tuple structure
        String abiJson = """
            [
              {
                "type": "event",
                "name": "TestEvent",
                "inputs": [
                  {
                    "type": "tuple",
                    "name": "testData",
                    "indexed": false,
                    "components": [
                      {
                        "type": "uint256",
                        "name": "amount"
                      },
                      {
                        "type": "string",
                        "name": "description"
                      },
                      {
                        "type": "bytes32",
                        "name": "id"
                      }
                    ]
                  }
                ]
              }
            ]
            """;

        // Parse the ABI
        Map<String, AbiParser.ContractAbiEvent> events = abiParser.parseAbi(abiJson);

        // Verify that the event was parsed successfully
        assertFalse(events.isEmpty(), "Events should not be empty");
        
        // Get the first event
        AbiParser.ContractAbiEvent contractEvent = events.values().iterator().next();
        assertNotNull(contractEvent, "Contract event should not be null");
        
        // Verify the event has the correct structure
        List<AbiParser.AbiEventInput> inputs = contractEvent.getInputs();
        assertEquals(1, inputs.size(), "Should have exactly one input");
        
        AbiParser.AbiEventInput tupleInput = inputs.get(0);
        assertEquals("testData", tupleInput.getName());
        assertEquals("tuple", tupleInput.getType());
        assertTrue(tupleInput.isTuple(), "Input should be identified as tuple");
        
        // Verify tuple components
        List<AbiParser.AbiEventInput> components = tupleInput.getComponents();
        assertNotNull(components, "Tuple components should not be null");
        assertEquals(3, components.size(), "Tuple should have 3 components");
        
        // Verify component details
        assertEquals("amount", components.get(0).getName());
        assertEquals("uint256", components.get(0).getType());
        
        assertEquals("description", components.get(1).getName());
        assertEquals("string", components.get(1).getType());
        
        assertEquals("id", components.get(2).getName());
        assertEquals("bytes32", components.get(2).getType());
    }

    @Test
    void testTupleArrayTypeReferenceCreation() throws IOException {
        // Test ABI JSON with a tuple array structure
        String abiJson = """
            [
              {
                "type": "event",
                "name": "TestArrayEvent",
                "inputs": [
                  {
                    "type": "tuple[]",
                    "name": "testDataArray",
                    "indexed": false,
                    "components": [
                      {
                        "type": "uint256",
                        "name": "value"
                      },
                      {
                        "type": "address",
                        "name": "account"
                      }
                    ]
                  }
                ]
              }
            ]
            """;

        // Parse the ABI
        Map<String, AbiParser.ContractAbiEvent> events = abiParser.parseAbi(abiJson);

        // Verify that the event was parsed successfully
        assertFalse(events.isEmpty(), "Events should not be empty");
        
        AbiParser.ContractAbiEvent contractEvent = events.values().iterator().next();
        List<AbiParser.AbiEventInput> inputs = contractEvent.getInputs();
        assertEquals(1, inputs.size(), "Should have exactly one input");
        
        AbiParser.AbiEventInput tupleArrayInput = inputs.get(0);
        assertEquals("testDataArray", tupleArrayInput.getName());
        assertEquals("tuple[]", tupleArrayInput.getType());
        assertTrue(tupleArrayInput.isTuple(), "Input should be identified as tuple");
        
        // Verify tuple components
        List<AbiParser.AbiEventInput> components = tupleArrayInput.getComponents();
        assertNotNull(components, "Tuple components should not be null");
        assertEquals(2, components.size(), "Tuple should have 2 components");
    }

    @Test
    void testTransferEventCompatibility() throws IOException {
        // Test that the original Transfer event still works with the new generic approach
        String transferAbiJson = """
            [
              {
                "type": "event",
                "name": "Transfer",
                "inputs": [
                  {
                    "type": "tuple",
                    "name": "transferData",
                    "indexed": false,
                    "components": [
                      {
                        "type": "bytes32",
                        "name": "transferType"
                      },
                      {
                        "type": "uint16",
                        "name": "zoneId"
                      },
                      {
                        "type": "bytes32",
                        "name": "fromValidatorId"
                      },
                      {
                        "type": "bytes32",
                        "name": "toValidatorId"
                      },
                      {
                        "type": "uint256",
                        "name": "fromAccountBalance"
                      },
                      {
                        "type": "uint256",
                        "name": "toAccountBalance"
                      },
                      {
                        "type": "uint256",
                        "name": "businessZoneBalance"
                      },
                      {
                        "type": "uint16",
                        "name": "bizZoneId"
                      },
                      {
                        "type": "bytes32",
                        "name": "sendAccountId"
                      },
                      {
                        "type": "bytes32",
                        "name": "fromAccountId"
                      },
                      {
                        "type": "string",
                        "name": "fromAccountName"
                      },
                      {
                        "type": "bytes32",
                        "name": "toAccountId"
                      },
                      {
                        "type": "string",
                        "name": "toAccountName"
                      },
                      {
                        "type": "uint256",
                        "name": "amount"
                      },
                      {
                        "type": "bytes32",
                        "name": "miscValue1"
                      },
                      {
                        "type": "string",
                        "name": "miscValue2"
                      },
                      {
                        "type": "string",
                        "name": "memo"
                      }
                    ]
                  },
                  {
                    "type": "bytes32",
                    "name": "traceId",
                    "indexed": false
                  }
                ]
              }
            ]
            """;

        // Parse the ABI
        Map<String, AbiParser.ContractAbiEvent> events = abiParser.parseAbi(transferAbiJson);

        // Verify that the Transfer event was parsed successfully
        assertFalse(events.isEmpty(), "Events should not be empty");
        
        AbiParser.ContractAbiEvent contractEvent = events.values().iterator().next();
        List<AbiParser.AbiEventInput> inputs = contractEvent.getInputs();
        assertEquals(2, inputs.size(), "Transfer event should have 2 inputs");
        
        // Verify the tuple input
        AbiParser.AbiEventInput tupleInput = inputs.get(0);
        assertEquals("transferData", tupleInput.getName());
        assertEquals("tuple", tupleInput.getType());
        assertTrue(tupleInput.isTuple(), "First input should be a tuple");
        
        // Verify tuple has all 17 components
        List<AbiParser.AbiEventInput> components = tupleInput.getComponents();
        assertNotNull(components, "Tuple components should not be null");
        assertEquals(17, components.size(), "Transfer tuple should have 17 components");
        
        // Verify the traceId input
        AbiParser.AbiEventInput traceIdInput = inputs.get(1);
        assertEquals("traceId", traceIdInput.getName());
        assertEquals("bytes32", traceIdInput.getType());
        assertFalse(traceIdInput.isTuple(), "Second input should not be a tuple");
    }
}
